#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试分机通讯的完整流程
"""

import asyncio
import subprocess
import time
import signal
import sys
from extension_client import AsyncModbusExtensionClient


async def test_extension_communication():
    """测试分机通讯"""
    print("=== 分机通讯测试 ===")
    
    # 创建分机客户端
    client = AsyncModbusExtensionClient(
        host="127.0.0.1",
        port=3456,
        serial_port="dev/ttyS2",
        baudrate=9600,
        timeout=1000,
        framer='rtu',
        retries=3,
    )
    
    try:
        print("正在连接到分机...")
        
        # 连接到分机
        if await client.connect():
            print("✓ 成功连接到分机")
            
            # 测试读取传感器数据
            print("\n正在读取传感器数据...")
            response = await client.read_holding_registers(
                address=10000,
                count=16,
                slave=1
            )
            
            if not response.isError():
                print("✓ 成功读取传感器数据")
                print(f"寄存器数据: {response.registers}")
                
                # 解析传感器数据
                print("\n解析传感器数据:")
                for i in range(0, len(response.registers), 4):
                    if i + 3 < len(response.registers):
                        cable_node = response.registers[i]
                        cable = cable_node >> 8
                        node = cable_node & 0xFF
                        temperature = response.registers[i + 1] / 10.0
                        humidity = response.registers[i + 2] / 10.0
                        moisture = response.registers[i + 3] / 100.0
                        
                        sensor_num = i // 4 + 1
                        print(f"传感器{sensor_num}: 缆{cable}节点{node} - "
                              f"温度{temperature:.1f}°C, 湿度{humidity:.1f}%, 水分{moisture:.2f}%")
            else:
                print(f"✗ 读取传感器数据失败: {response}")
        else:
            print("✗ 连接分机失败")
    
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
    
    finally:
        client.close()
        print("\n分机连接已关闭")


def start_mock_server():
    """启动模拟分机服务器"""
    print("启动模拟分机服务器...")
    process = subprocess.Popen([
        sys.executable, "mock_extension_server.py"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # 等待服务器启动
    time.sleep(2)
    
    return process


def stop_mock_server(process):
    """停止模拟分机服务器"""
    if process:
        print("停止模拟分机服务器...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()


async def main():
    """主测试函数"""
    server_process = None
    
    try:
        # 启动模拟服务器
        server_process = start_mock_server()
        
        # 运行通讯测试
        await test_extension_communication()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在退出...")
    
    finally:
        # 停止模拟服务器
        stop_mock_server(server_process)


if __name__ == "__main__":
    asyncio.run(main())
