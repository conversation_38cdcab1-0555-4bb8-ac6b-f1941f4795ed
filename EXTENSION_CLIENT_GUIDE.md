# 分机通讯客户端使用指南

## 概述

`AsyncModbusExtensionClient` 是一个专门用于通过分机进行Modbus通讯的异步客户端类。分机是一个TCP/IP转RS485的设备，内置了自定义加密方法。

## 分机通讯原理

### 1. 分机设备特性
- 分机有服务程序侦听3456端口
- 客户端程序可以连接到此端口，发送命令，并取得执行结果
- 内置自定义加密方法用于数据传输安全

### 2. 加密方法
分机使用对称加密方法：
1. 将待加密内容按字节前后倒转（第一个字节变最后一个）
2. 对每个字节求补码

这个加密方法是对称的，即加密内容经过同样处理后即复原为未加密内容。

### 3. rs485命令格式
分机内置一个rs485命令，可以将待发送内容发送到串口设备：

```
rs485 -p dev/ttyS2 -b 9600 -t 1000 XX XX XX XX ...
```

参数说明：
- `-p dev/ttyS2`: 串口设备路径
- `-b 9600`: 波特率
- `-t 1000`: 超时时间(毫秒)
- `XX XX XX XX`: 需要发送到设备的字节数据，即modbus协议RTU格式的命令字节数据的十六进制表示

设备执行结果格式为`XX XX XX XX XX`（modbus协议RTU格式的命令结果数据）的十六进制表示。

## 使用方法

### 1. 基本用法

```python
from extension_client import AsyncModbusExtensionClient
import asyncio

async def main():
    # 创建分机客户端
    client = AsyncModbusExtensionClient(
        host="*************",          # 分机IP地址
        port=3456,                     # 分机端口
        serial_port="dev/ttyS2",       # 分机串口设备路径
        baudrate=9600,                 # 串口波特率
        timeout=1000,                  # 超时时间(毫秒)
        framer='rtu',                  # 使用RTU帧格式
        retries=3,                     # 重试次数
    )
    
    try:
        # 连接到分机
        if await client.connect():
            print("成功连接到分机")
            
            # 读取传感器数据
            response = await client.read_holding_registers(
                address=10000,
                count=16,
                slave=1
            )
            
            if not response.isError():
                print(f"读取到数据: {response.registers}")
            else:
                print(f"读取失败: {response}")
        else:
            print("连接分机失败")
    
    finally:
        client.close()

# 运行示例
asyncio.run(main())
```

### 2. 在sensor_reader.py中使用

```bash
# 使用分机通讯读取传感器数据
python sensor_reader.py --extension ************* --count 4 --verbose

# 指定分机参数
python sensor_reader.py \
    --extension ************* \
    --extension-port 3456 \
    --extension-serial dev/ttyS2 \
    --extension-baudrate 9600 \
    --extension-timeout 1000 \
    --count 4 \
    --verbose
```

### 3. 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--extension` | 无 | 分机IP地址 |
| `--extension-port` | 3456 | 分机端口 |
| `--extension-serial` | dev/ttyS2 | 分机串口设备路径 |
| `--extension-baudrate` | 9600 | 分机串口波特率 |
| `--extension-timeout` | 1000 | 分机超时时间(毫秒) |

## 调试功能

使用 `--verbose` 参数可以显示详细的通讯协议字节内容，包括：
- 发送和接收的数据包字节内容
- PDU信息
- 连接状态信息

```bash
python sensor_reader.py --extension ************* --verbose
```

## 错误处理

客户端会自动处理以下错误情况：
1. 连接失败自动重试
2. 通讯超时处理
3. 数据解析错误处理
4. 分机响应格式错误处理

## 兼容性

- 与现有的串口和TCP/IP通讯方式完全兼容
- 支持所有标准的Modbus功能
- 保持与原有代码的接口一致性

## 测试

运行测试脚本验证功能：

```bash
python test_extension_client.py
```

测试内容包括：
- 加密/解密功能验证
- rs485命令构建测试
- 响应解析测试
