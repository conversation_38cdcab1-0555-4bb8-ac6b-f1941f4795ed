#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模拟分机服务器
用于测试分机通讯客户端
"""

import asyncio
import socket
import threading
import time
from datetime import datetime


class MockExtensionServer:
    """模拟分机服务器"""
    
    def __init__(self, host="127.0.0.1", port=3456):
        self.host = host
        self.port = port
        self.server = None
        self.running = False
        
    def _encrypt_decrypt_data(self, data: bytes) -> bytes:
        """
        分机加密/解密方法（对称）
        1. 将字节前后倒转（第一个字节变最后一个）
        2. 对每个字节求补码
        """
        if not data:
            return data
            
        # 步骤1: 字节前后倒转
        reversed_data = data[::-1]
        
        # 步骤2: 对每个字节求补码
        encrypted_data = bytes(~b & 0xFF for b in reversed_data)
        
        return encrypted_data
    
    def _parse_rs485_command(self, command: str) -> bytes:
        """解析rs485命令并返回模拟响应"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 收到rs485命令: {command}")
        
        # 解析命令参数
        parts = command.strip().split()
        if len(parts) < 7 or parts[0] != "rs485":
            return b"ERROR: Invalid command"
        
        # 提取十六进制数据部分
        hex_data_parts = parts[7:]  # 跳过 "rs485 -p dev/ttyS2 -b 9600 -t 1000"
        
        try:
            # 将十六进制字符串转换为字节
            request_data = bytes(int(part, 16) for part in hex_data_parts)
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 解析的请求数据: {request_data.hex().upper()}")
            
            # 模拟Modbus响应
            if len(request_data) >= 6:
                slave_id = request_data[0]
                function_code = request_data[1]
                
                if function_code == 0x03:  # 读保持寄存器
                    # 模拟传感器数据响应
                    response_data = self._generate_sensor_response(slave_id)
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] 生成响应数据: {response_data.hex().upper()}")
                    return response_data
            
            # 默认响应
            return bytes.fromhex("01 03 02 00 01 79 84")
            
        except ValueError as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 解析错误: {e}")
            return b"ERROR: Invalid hex data"
    
    def _generate_sensor_response(self, slave_id: int) -> bytes:
        """生成模拟的传感器数据响应"""
        # 模拟4个传感器的数据
        # 每个传感器: [缆号, 节点号, 温度, 湿度, 水分]
        sensors_data = [
            [0x01, 0x01, 250, 399, 1038],  # 25.0°C, 39.9%, 10.38%
            [0x01, 0x02, 251, 383, 1020],  # 25.1°C, 38.3%, 10.20%
            [0x01, 0x03, 251, 376, 1004],  # 25.1°C, 37.6%, 10.04%
            [0x01, 0x04, 250, 377, 1006],  # 25.0°C, 37.7%, 10.06%
        ]
        
        # 构建响应
        response = [slave_id, 0x03, 0x20]  # 从站ID, 功能码, 数据长度(32字节)
        
        for sensor in sensors_data:
            cable_node = (sensor[0] << 8) | sensor[1]
            response.extend([
                (cable_node >> 8) & 0xFF, cable_node & 0xFF,  # 缆号+节点号
                (sensor[2] >> 8) & 0xFF, sensor[2] & 0xFF,    # 温度
                (sensor[3] >> 8) & 0xFF, sensor[3] & 0xFF,    # 湿度
                (sensor[4] >> 8) & 0xFF, sensor[4] & 0xFF,    # 水分
            ])
        
        # 计算CRC
        response_bytes = bytes(response)
        crc = self._calculate_crc(response_bytes)
        response.extend([crc & 0xFF, (crc >> 8) & 0xFF])
        
        return bytes(response)
    
    def _calculate_crc(self, data: bytes) -> int:
        """计算Modbus RTU CRC"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc >>= 1
                    crc ^= 0xA001
                else:
                    crc >>= 1
        return crc
    
    async def handle_client(self, reader, writer):
        """处理客户端连接"""
        client_addr = writer.get_extra_info('peername')
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 客户端连接: {client_addr}")
        
        try:
            while self.running:
                # 接收加密的数据
                encrypted_data = await reader.read(1024)
                if not encrypted_data:
                    break
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 收到加密数据: {encrypted_data.hex().upper()}")
                
                # 解密数据
                decrypted_data = self._encrypt_decrypt_data(encrypted_data)
                command = decrypted_data.decode('gbk', errors='ignore')
                
                # 处理rs485命令
                response_data = self._parse_rs485_command(command)
                
                # 将响应转换为十六进制字符串
                response_str = ' '.join(f'{b:02X}' for b in response_data)
                
                # 加密响应
                encrypted_response = self._encrypt_decrypt_data(response_str.encode('gbk'))
                
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送加密响应: {encrypted_response.hex().upper()}")
                
                # 发送加密的响应
                writer.write(encrypted_response)
                await writer.drain()
                
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 处理客户端错误: {e}")
        finally:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 客户端断开: {client_addr}")
            writer.close()
            await writer.wait_closed()
    
    async def start(self):
        """启动服务器"""
        self.running = True
        self.server = await asyncio.start_server(
            self.handle_client, self.host, self.port
        )
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 模拟分机服务器启动在 {self.host}:{self.port}")
        
        async with self.server:
            await self.server.serve_forever()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.server:
            self.server.close()


async def main():
    """主函数"""
    server = MockExtensionServer()
    
    try:
        await server.start()
    except KeyboardInterrupt:
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 收到中断信号，正在停止服务器...")
        server.stop()


if __name__ == "__main__":
    asyncio.run(main())
