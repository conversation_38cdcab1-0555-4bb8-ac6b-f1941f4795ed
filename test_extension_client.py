#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试分机客户端的加密/解密功能
"""

import asyncio
from extension_client import AsyncModbusExtensionClient


async def test_encrypt_decrypt():
    """测试加密/解密功能"""
    client = AsyncModbusExtensionClient("127.0.0.1")
    
    # 测试数据
    test_cases = [
        b"hello",
        b"rs485 -p dev/ttyS2 -b 9600 -t 1000 01 03 27 10 00 10",
        b"\x01\x03\x27\x10\x00\x10",
        b"",
        b"A",
    ]
    
    print("测试加密/解密功能:")
    print("=" * 50)
    
    for i, original in enumerate(test_cases):
        print(f"\n测试案例 {i+1}:")
        print(f"原始数据: {original}")
        print(f"原始十六进制: {original.hex().upper()}")
        
        # 加密
        encrypted = client._encrypt_decrypt_data(original)
        print(f"加密后: {encrypted}")
        print(f"加密十六进制: {encrypted.hex().upper()}")
        
        # 解密（应该恢复原始数据）
        decrypted = client._encrypt_decrypt_data(encrypted)
        print(f"解密后: {decrypted}")
        print(f"解密十六进制: {decrypted.hex().upper()}")
        
        # 验证
        if original == decrypted:
            print("✓ 加密/解密成功")
        else:
            print("✗ 加密/解密失败")


async def test_rs485_command():
    """测试rs485命令构建"""
    client = AsyncModbusExtensionClient("127.0.0.1")
    
    print("\n\n测试rs485命令构建:")
    print("=" * 50)
    
    # 测试Modbus RTU数据
    test_data = bytes.fromhex("01 03 27 10 00 10 C4 0E")
    print(f"Modbus数据: {test_data.hex().upper()}")
    
    command = client._build_rs485_command(test_data)
    print(f"rs485命令: {command}")
    
    # 测试响应解析
    response_str = "01 03 20 01 01 00 FA 01 8F 04 0E 01 02 00 FB 01 83 03 FC 01 03 00 FB 01 78 03 EC 01 04 00 FA 01 79 03 EE B2 3D"
    print(f"\n响应字符串: {response_str}")
    
    try:
        response_data = client._parse_rs485_response(response_str)
        print(f"解析后数据: {response_data.hex().upper()}")
        print("✓ 响应解析成功")
    except Exception as e:
        print(f"✗ 响应解析失败: {e}")


async def main():
    """主测试函数"""
    await test_encrypt_decrypt()
    await test_rs485_command()


if __name__ == "__main__":
    asyncio.run(main())
