
sensor_reader.py是一个根据MODBUS协议从设备读取温度/湿度/水分数值的程序，支持串口和TCP/IP方式。
现在根据现场需要，增加一种通过分机进行通讯的方式。
所谓分机，是一个TCP/IP转RS485的设备，内置了自定义加密方法。
1、分机有服务程序侦听3456端口。客户端程序可以连接到此端口，发送命令，并取得执行结果。
2、底层加密方法：将待加密内容按字节前后倒转（第一个字节变最后一个），并对每个字节求补码。这个加密方法是对称的，即加密内容经过同样处理后即复原为未加密内容。
3、分机内置一个rs485命令，可以将待发送内容发送到串口设备。命令格式:
    rs485 -p dev/ttyS2 -b 9600 -t 1000 XX XX XX XX ...
    XX XX XX XX 为需要发送到设备的字节数据，即modbus协议RTU格式的命令字节数据的十六进制表示
    设备执行结果格式为XX XX XX XX XX（modbus协议RTU格式的命令结果数据）的十六进制表示。
为了和原有代码兼容，我推荐另建一个py文件，从pymodbus的ModbusBaseClient继承一个子类，实现一个通过分机通讯的Modbus异步通讯客户端类，主程序使用这个子类就行。
