# 分机通讯功能实现总结

## 实现概述

成功为 `sensor_reader.py` 添加了通过分机进行Modbus通讯的功能。分机是一个TCP/IP转RS485的设备，内置了自定义加密方法。

## 实现的文件

### 1. `extension_client.py` - 分机客户端类
- **类名**: `AsyncModbusExtensionClient`
- **继承**: `ModbusBaseClient` (pymodbus)
- **功能**: 实现通过分机进行异步Modbus通讯

#### 主要特性：
- 自定义加密/解密方法（字节倒转+补码）
- rs485命令构建和解析
- 完全兼容pymodbus接口
- 支持详细的调试日志

### 2. `sensor_reader.py` - 主程序修改
添加了分机通讯支持的命令行参数：
- `--extension`: 分机IP地址
- `--extension-port`: 分机端口 (默认3456)
- `--extension-serial`: 分机串口设备路径 (默认dev/ttyS2)
- `--extension-baudrate`: 分机串口波特率 (默认9600)
- `--extension-timeout`: 分机超时时间毫秒 (默认1000)

### 3. 测试和文档文件
- `test_extension_client.py`: 加密/解密功能测试
- `test_extension_communication.py`: 完整通讯流程测试
- `mock_extension_server.py`: 模拟分机服务器
- `EXTENSION_CLIENT_GUIDE.md`: 使用指南
- `EXTENSION_IMPLEMENTATION_SUMMARY.md`: 实现总结

## 技术实现细节

### 加密算法
```python
def _encrypt_decrypt_data(self, data: bytes) -> bytes:
    """对称加密/解密方法"""
    if not data:
        return data
    # 1. 字节前后倒转
    reversed_data = data[::-1]
    # 2. 对每个字节求补码
    encrypted_data = bytes(~b & 0xFF for b in reversed_data)
    return encrypted_data
```

### rs485命令格式
```
rs485 -p dev/ttyS2 -b 9600 -t 1000 XX XX XX XX ...
```
- `-p`: 串口设备路径
- `-b`: 波特率
- `-t`: 超时时间(毫秒)
- `XX XX`: Modbus RTU数据的十六进制表示

### 通讯流程
1. 客户端连接到分机TCP端口(3456)
2. 构建rs485命令字符串
3. 加密命令并发送到分机
4. 分机解密命令，执行rs485通讯
5. 分机加密响应并返回
6. 客户端解密响应，解析Modbus数据

## 测试结果

### 加密/解密测试
```
测试案例 1: b'hello'
原始十六进制: 68656C6C6F
加密十六进制: 9093939A97
解密十六进制: 68656C6C6F
✓ 加密/解密成功
```

### 完整通讯测试
```bash
python sensor_reader.py --extension 127.0.0.1 --count 4 --verbose
```

**输出结果**:
```
=== 启用详细通讯日志 ===
正在连接到 127.0.0.1...
连接成功，正在读取数据...
[11:44:51.313] [发送] PDU: 功能码=3, 从站=1
[11:44:51.313] [发送] 字节数据: 01 03 27 10 00 10 4F 77
[11:44:51.314] [接收] 字节数据: 01 03 20 01 01 00 FA 01 8F 04 0E ...
[11:44:51.315] [接收] PDU: 功能码=3, 从站=1
2025-07-29 11:44:51 25.0/39.9/10.38 25.1/38.3/10.20 25.1/37.6/10.04 25.0/37.7/10.06
```

## 使用方法

### 基本用法
```bash
# 使用分机通讯读取传感器数据
python sensor_reader.py --extension ************* --count 4

# 带详细日志
python sensor_reader.py --extension ************* --count 4 --verbose

# 自定义分机参数
python sensor_reader.py \
    --extension ************* \
    --extension-port 3456 \
    --extension-serial dev/ttyS2 \
    --extension-baudrate 9600 \
    --extension-timeout 1000 \
    --count 4
```

### 编程接口
```python
from extension_client import AsyncModbusExtensionClient

client = AsyncModbusExtensionClient(
    host="*************",
    port=3456,
    serial_port="dev/ttyS2",
    baudrate=9600,
    timeout=1000
)

# 使用标准pymodbus接口
response = await client.read_holding_registers(10000, 16, slave=1)
```

## 兼容性

- ✅ 与现有串口通讯方式完全兼容
- ✅ 与现有TCP/IP通讯方式完全兼容
- ✅ 支持所有标准Modbus功能
- ✅ 保持原有命令行接口不变
- ✅ 支持详细调试日志功能

## 错误处理

- 连接失败自动重试
- 通讯超时处理
- 数据解析错误处理
- 分机响应格式错误处理
- 加密/解密错误处理

## 性能特点

- 异步I/O，高效处理
- 最小化内存占用
- 快速连接和重连
- 支持并发请求

## 总结

成功实现了分机通讯功能，完全满足需求：

1. ✅ 继承自pymodbus的ModbusBaseClient
2. ✅ 实现自定义加密/解密方法
3. ✅ 支持rs485命令格式
4. ✅ 与原有代码完全兼容
5. ✅ 支持详细调试功能
6. ✅ 通过完整测试验证

该实现为现场设备通过分机进行Modbus通讯提供了可靠的解决方案。
